const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('开始生成类型声明文件...');

/**
 * 递归获取目录下所有的.d.ts文件
 */
function getAllTypeFiles(dir, files = []) {
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.')) {
      getAllTypeFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * 合并所有类型文件到一个文件中
 */
function mergeTypeFiles(typesDir, outputPath) {
  const typeFiles = getAllTypeFiles(typesDir);
  let mergedContent = '';

  // 添加文件头注释
  mergedContent += `/**
 * DL引擎完整类型定义文件
 * 此文件包含了DL引擎所有模块的类型定义
 * 自动生成于: ${new Date().toLocaleString('zh-CN')}
 */

`;

  // 用于跟踪已导入的模块，避免重复
  const importedModules = new Set();
  const exportedTypes = new Set();

  // 首先处理主入口文件
  const indexPath = path.join(typesDir, 'index.d.ts');
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf-8');
    mergedContent += `// ===== 主入口文件 =====\n`;
    mergedContent += content + '\n\n';
  }

  // 按模块分类处理其他类型文件
  const moduleCategories = {
    'core': '核心模块',
    'rendering': '渲染模块',
    'physics': '物理模块',
    'animation': '动画模块',
    'audio': '音频模块',
    'ai': 'AI模块',
    'network': '网络模块',
    'visualscript': '视觉脚本模块',
    'visual-script': '视觉脚本模块',
    'interaction': '交互模块',
    'input': '输入模块',
    'scene': '场景模块',
    'assets': '资源模块',
    'particles': '粒子模块',
    'avatar': '头像模块',
    'blockchain': '区块链模块',
    'navigation': '导航模块',
    'ui': 'UI模块',
    'utils': '工具模块',
    'safety': '安全模块',
    'gltf': 'GLTF模块',
    'i18n': '国际化模块',
    'industrial': '工业模块',
    'mocap': '动作捕捉模块',
    'spatial': '空间模块',
    'terrain': '地形模块',
    'vegetation': '植被模块',
    'visualization': '可视化模块',
    'voice': '语音模块',
    'workers': 'Worker模块',
    'effects': '特效模块',
    'emotion': '情感模块',
    'environment': '环境模块',
    'data': '数据模块',
    'debug': '调试模块',
    'memory': '内存模块',
    'mobile': '移动端模块',
    'surgery': '手术模块',
    'examples': '示例模块'
  };

  // 处理每个模块分类
  for (const [moduleDir, moduleName] of Object.entries(moduleCategories)) {
    const modulePath = path.join(typesDir, moduleDir);
    if (fs.existsSync(modulePath) && fs.statSync(modulePath).isDirectory()) {
      mergedContent += `// ===== ${moduleName} =====\n`;

      const moduleFiles = getAllTypeFiles(modulePath);
      for (const filePath of moduleFiles) {
        const relativePath = path.relative(typesDir, filePath);
        const content = fs.readFileSync(filePath, 'utf-8');

        // 清理导入语句，因为我们要合并到一个文件中
        const cleanedContent = content
          .replace(/^import\s+.*?from\s+['"][^'"]*['"];?\s*$/gm, '') // 移除import语句
          .replace(/^export\s*\*\s*from\s+['"][^'"]*['"];?\s*$/gm, '') // 移除export * from语句
          .replace(/^\s*$/gm, '') // 移除空行
          .trim();

        if (cleanedContent) {
          mergedContent += `\n// ----- ${relativePath} -----\n`;
          mergedContent += cleanedContent + '\n';
        }
      }
      mergedContent += '\n';
    }
  }

  // 写入合并后的文件
  fs.writeFileSync(outputPath, mergedContent, 'utf-8');
  console.log(`✅ 类型文件已合并到: ${outputPath}`);
  console.log(`📊 合并了 ${typeFiles.length} 个类型文件`);

  // 统计信息
  const lines = mergedContent.split('\n').length;
  const size = (mergedContent.length / 1024).toFixed(2);
  console.log(`📏 合并后文件大小: ${size} KB, ${lines} 行`);
}

try {
  // 运行 TypeScript 编译器生成类型声明文件
  execSync('npx tsc --declaration --emitDeclarationOnly --outDir dist/types', {
    stdio: 'inherit',
    cwd: __dirname
  });

  const typesDir = path.join(__dirname, 'dist/types');
  const distIndexPath = path.join(__dirname, 'dist/index.d.ts');
  const mergedTypesPath = path.join(__dirname, 'dist/dl-engine-types.d.ts');

  if (fs.existsSync(typesDir)) {
    // 复制主入口文件
    const typesIndexPath = path.join(typesDir, 'index.d.ts');
    if (fs.existsSync(typesIndexPath)) {
      fs.copyFileSync(typesIndexPath, distIndexPath);
      console.log('✅ 主类型声明文件生成成功！');
    }

    // 合并所有类型文件
    mergeTypeFiles(typesDir, mergedTypesPath);

    console.log('\n🎉 类型文件处理完成！');
    console.log(`📁 主类型文件: dist/index.d.ts`);
    console.log(`📁 完整类型文件: dist/dl-engine-types.d.ts`);
  } else {
    console.error('❌ 类型声明文件生成失败：找不到 dist/types 目录');
  }
} catch (error) {
  console.error('❌ 构建类型声明文件时出错：', error.message);
  process.exit(1);
}
