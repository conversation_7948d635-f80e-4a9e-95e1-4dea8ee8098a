const fs = require('fs');
const path = require('path');

console.log('开始导出纯类型定义文件...');

/**
 * 递归获取目录下所有的.d.ts文件
 */
function getAllTypeFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.')) {
      getAllTypeFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 清理类型定义内容，移除import/export语句，只保留纯类型定义
 */
function cleanTypeContent(content) {
  return content
    // 移除import语句
    .replace(/^import\s+.*?from\s+['"][^'"]*['"];?\s*$/gm, '')
    // 移除export语句但保留类型定义
    .replace(/^export\s*\*\s*from\s+['"][^'"]*['"];?\s*$/gm, '')
    // 移除export关键字但保留类型定义
    .replace(/^export\s+(interface|type|enum|class|const|function|namespace|declare)/gm, '$1')
    .replace(/^export\s+\{[^}]*\};?\s*$/gm, '')
    .replace(/^export\s+type\s+\{[^}]*\};?\s*$/gm, '')
    // 移除多余的空行
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();
}

/**
 * 导出纯类型定义文件
 */
function exportCleanTypes(typesDir, outputPath) {
  const typeFiles = getAllTypeFiles(typesDir);
  let cleanContent = '';
  
  // 添加文件头注释
  cleanContent += `/**
 * DL引擎纯类型定义文件
 * 此文件仅包含类型定义，不包含import/export语句
 * 可直接用于TypeScript项目的类型声明
 * 自动生成于: ${new Date().toLocaleString('zh-CN')}
 * 
 * 使用方法:
 * 1. 将此文件复制到你的项目中
 * 2. 在tsconfig.json中添加类型引用
 * 3. 或者使用 /// <reference path="./dl-engine-clean-types.d.ts" />
 */

declare namespace DLEngine {

`;

  // 模块分类
  const moduleCategories = {
    'core': '核心模块',
    'rendering': '渲染模块', 
    'physics': '物理模块',
    'animation': '动画模块',
    'audio': '音频模块',
    'ai': 'AI模块',
    'network': '网络模块',
    'visualscript': '视觉脚本模块',
    'visual-script': '视觉脚本模块',
    'interaction': '交互模块',
    'input': '输入模块',
    'scene': '场景模块',
    'assets': '资源模块',
    'particles': '粒子模块',
    'avatar': '头像模块',
    'blockchain': '区块链模块',
    'navigation': '导航模块',
    'ui': 'UI模块',
    'utils': '工具模块',
    'safety': '安全模块'
  };

  // 处理每个模块分类
  for (const [moduleDir, moduleName] of Object.entries(moduleCategories)) {
    const modulePath = path.join(typesDir, moduleDir);
    if (fs.existsSync(modulePath) && fs.statSync(modulePath).isDirectory()) {
      cleanContent += `  // ===== ${moduleName} =====\n`;
      cleanContent += `  namespace ${moduleDir.replace('-', '_')} {\n`;
      
      const moduleFiles = getAllTypeFiles(modulePath);
      for (const filePath of moduleFiles) {
        const relativePath = path.relative(typesDir, filePath);
        const content = fs.readFileSync(filePath, 'utf-8');
        const cleanedContent = cleanTypeContent(content);
        
        if (cleanedContent && cleanedContent.length > 10) {
          const fileName = path.basename(filePath, '.d.ts');
          cleanContent += `\n    // ----- ${fileName} -----\n`;
          // 缩进内容
          const indentedContent = cleanedContent
            .split('\n')
            .map(line => line ? `    ${line}` : line)
            .join('\n');
          cleanContent += indentedContent + '\n';
        }
      }
      cleanContent += `  }\n\n`;
    }
  }
  
  cleanContent += '}\n\n';
  
  // 添加全局类型导出
  cleanContent += `// 全局类型导出，方便直接使用
declare global {
  namespace DL {
    // 核心类型
    type Engine = DLEngine.core.Engine;
    type World = DLEngine.core.World;
    type Entity = DLEngine.core.Entity;
    type Component = DLEngine.core.Component;
    type System = DLEngine.core.System;
    
    // 渲染类型
    type Camera = DLEngine.rendering.Camera;
    type Renderer = DLEngine.rendering.Renderer;
    type Light = DLEngine.rendering.Light;
    
    // 物理类型
    type PhysicsBody = DLEngine.physics.PhysicsBody;
    type PhysicsCollider = DLEngine.physics.PhysicsCollider;
    
    // 动画类型
    type Animator = DLEngine.animation.Animator;
    type AnimationClip = DLEngine.animation.AnimationClip;
    
    // 视觉脚本类型
    type VisualScriptSystem = DLEngine.visualscript.VisualScriptSystem;
    type Node = DLEngine.visualscript.Node;
  }
}

export {};
`;
  
  // 写入文件
  fs.writeFileSync(outputPath, cleanContent, 'utf-8');
  console.log(`✅ 纯类型定义文件已导出到: ${outputPath}`);
  
  // 统计信息
  const lines = cleanContent.split('\n').length;
  const size = (cleanContent.length / 1024).toFixed(2);
  console.log(`📏 文件大小: ${size} KB, ${lines} 行`);
  console.log(`📊 处理了 ${typeFiles.length} 个类型文件`);
}

try {
  const typesDir = path.join(__dirname, 'dist/types');
  const cleanTypesPath = path.join(__dirname, 'dist/dl-engine-clean-types.d.ts');
  
  if (fs.existsSync(typesDir)) {
    exportCleanTypes(typesDir, cleanTypesPath);
    console.log('\n🎉 纯类型定义文件导出完成！');
    console.log(`📁 文件位置: dist/dl-engine-clean-types.d.ts`);
  } else {
    console.error('❌ 找不到 dist/types 目录，请先运行 build-types.js');
  }
} catch (error) {
  console.error('❌ 导出类型文件时出错：', error.message);
  process.exit(1);
}
