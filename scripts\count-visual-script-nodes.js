/**
 * 统计视觉脚本系统中的节点数量
 */
const fs = require('fs');
const path = require('path');

// 节点文件路径
const nodeFilesPath = 'engine/src/visualscript/presets';
const visualScriptNodesPath = 'engine/src/visual-script/nodes';

// 节点统计结果
const nodeStats = {
  totalNodes: 0,
  nodesByFile: {},
  nodesByCategory: {},
  allNodes: []
};

/**
 * 分析单个节点文件
 * @param {string} filePath 文件路径
 * @returns {object} 节点统计信息
 */
function analyzeNodeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.ts');
    
    // 查找类定义（节点类）
    const classRegex = /export\s+class\s+(\w+Node)\s+extends\s+/g;
    const nodes = [];
    let match;
    
    while ((match = classRegex.exec(content)) !== null) {
      nodes.push(match[1]);
    }
    
    // 查找注册函数中的节点类型
    const registerRegex = /registry\.registerNodeType\(\s*\{[^}]*type:\s*['"`]([^'"`]+)['"`]/g;
    const registeredTypes = [];
    
    while ((match = registerRegex.exec(content)) !== null) {
      registeredTypes.push(match[1]);
    }
    
    return {
      fileName,
      nodeClasses: nodes,
      registeredTypes,
      nodeCount: nodes.length,
      registeredCount: registeredTypes.length
    };
  } catch (error) {
    console.error(`分析文件 ${filePath} 时出错:`, error.message);
    return {
      fileName: path.basename(filePath, '.ts'),
      nodeClasses: [],
      registeredTypes: [],
      nodeCount: 0,
      registeredCount: 0
    };
  }
}

/**
 * 获取节点类别
 * @param {string} fileName 文件名
 * @returns {string} 节点类别
 */
function getNodeCategory(fileName) {
  const categoryMap = {
    'CoreNodes': '核心节点',
    'LogicNodes': '逻辑节点',
    'MathNodes': '数学节点',
    'EntityNodes': '实体节点',
    'PhysicsNodes': '物理节点',
    'SoftBodyNodes': '软体物理节点',
    'AnimationNodes': '动画节点',
    'InputNodes': '输入节点',
    'AudioNodes': '音频节点',
    'NetworkNodes': '网络节点',
    'HTTPNodes': 'HTTP节点',
    'JSONNodes': 'JSON节点',
    'DateTimeNodes': '日期时间节点',
    'UINodes': 'UI节点',
    'AdvancedUINodes': '高级UI节点',
    'FileSystemNodes': '文件系统节点',
    'AdvancedFileSystemNodes': '高级文件系统节点',
    'ImageProcessingNodes': '图像处理节点',
    'AdvancedImageNodes': '高级图像节点',
    'DatabaseNodes': '数据库节点',
    'CryptographyNodes': '加密节点',
    'AINodes': 'AI节点',
    'AIModelNodes': 'AI模型节点',
    'AIEmotionNodes': 'AI情感节点',
    'AINLPNodes': 'AI自然语言处理节点',
    'AIAssistantNodes': 'AI助手节点',
    'DistributedExecutionNodes': '分布式执行节点',
    'PerformanceMonitoringNodes': '性能监控节点',
    'PerformanceAnalysisNodes': '性能分析节点',
    'CollaborationNodes': '协作节点',
    'AdvancedDebuggingNodes': '高级调试节点',
    'DebugNodes': '调试节点',
    'TimeNodes': '时间节点',
    'WebRTCNodes': 'WebRTC节点',
    'NetworkProtocolNodes': '网络协议节点',
    'NetworkSecurityNodes': '网络安全节点'
  };
  
  return categoryMap[fileName] || '其他节点';
}

/**
 * 根据目录路径获取节点类别
 * @param {string} dirPath 目录路径
 * @returns {string} 节点类别
 */
function getCategoryFromPath(dirPath) {
  const pathMap = {
    'mocap': '动作捕捉节点',
    'entity': '实体节点',
    'physics': '物理节点',
    'rendering': '渲染节点',
    'animation': '动画节点',
    'audio': '音频节点',
    'input': '输入节点',
    'network': '网络节点',
    'ui': 'UI节点',
    'blockchain': '区块链节点',
    'learning': '学习记录节点',
    'spatial': '空间信息节点',
    'rag': 'RAG应用节点',
    'scene': '场景节点',
    'water': '水体系统节点',
    'particles': '粒子系统节点',
    'postprocessing': '后处理节点',
    'terrain': '地形系统节点',
    'industrial': '工业自动化节点',
    'smartcity': '智慧城市节点'
  };

  for (const [key, value] of Object.entries(pathMap)) {
    if (dirPath.includes(key)) {
      return value;
    }
  }

  return '其他节点';
}

/**
 * 递归分析目录中的节点文件
 */
function analyzeDirectory(dirPath, basePath = '') {
  const results = [];

  if (!fs.existsSync(dirPath)) {
    return results;
  }

  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // 递归分析子目录
      const subResults = analyzeDirectory(itemPath, path.join(basePath, item));
      results.push(...subResults);
    } else if (item.endsWith('.ts') && !item.includes('test') && !item.includes('spec')) {
      // 分析TypeScript文件
      const analysis = analyzeNodeFile(itemPath);
      if (analysis.nodeCount > 0) {
        analysis.relativePath = path.join(basePath, item);
        analysis.directory = basePath || 'root';
        results.push(analysis);
      }
    }
  }

  return results;
}

/**
 * 主函数
 */
function main() {
  console.log('=== 视觉脚本系统节点统计分析 ===\n');

  // 分析预设节点
  console.log('📂 分析预设节点目录...');
  const presetResults = analyzeDirectory(nodeFilesPath);

  // 分析visual-script节点
  console.log('📂 分析visual-script节点目录...');
  const visualScriptResults = analyzeDirectory(visualScriptNodesPath);

  // 合并结果
  const allResults = [...presetResults, ...visualScriptResults];

  console.log(`\n发现 ${allResults.length} 个包含节点的文件:\n`);

  // 分析每个文件
  allResults.forEach(analysis => {
    const category = getNodeCategory(analysis.fileName) || getCategoryFromPath(analysis.directory);

    nodeStats.nodesByFile[analysis.fileName] = analysis;
    nodeStats.totalNodes += analysis.nodeCount;

    if (!nodeStats.nodesByCategory[category]) {
      nodeStats.nodesByCategory[category] = 0;
    }
    nodeStats.nodesByCategory[category] += analysis.nodeCount;

    nodeStats.allNodes.push(...analysis.nodeClasses.map(node => ({
      name: node,
      file: analysis.fileName,
      category: category,
      directory: analysis.directory
    })));

    console.log(`📁 ${analysis.relativePath}`);
    console.log(`   目录: ${analysis.directory}`);
    console.log(`   类别: ${category}`);
    console.log(`   节点类数量: ${analysis.nodeCount}`);
    console.log(`   注册类型数量: ${analysis.registeredCount}`);
    if (analysis.nodeClasses.length > 0) {
      console.log(`   节点类: ${analysis.nodeClasses.join(', ')}`);
    }
    console.log('');
  });
  
  // 输出统计结果
  console.log('=== 统计结果 ===\n');
  console.log(`📊 总节点数量: ${nodeStats.totalNodes}`);
  console.log(`📁 节点文件数量: ${allResults.length}\n`);
  
  console.log('📋 按类别统计:');
  Object.entries(nodeStats.nodesByCategory)
    .sort((a, b) => b[1] - a[1])
    .forEach(([category, count]) => {
      console.log(`   ${category}: ${count} 个节点`);
    });
  
  console.log('\n📈 详细文件统计:');
  Object.entries(nodeStats.nodesByFile)
    .sort((a, b) => b[1].nodeCount - a[1].nodeCount)
    .forEach(([fileName, data]) => {
      console.log(`   ${fileName}: ${data.nodeCount} 个节点`);
    });
  
  // 保存详细报告
  const reportPath = 'docs/视觉脚本节点统计报告.md';
  generateDetailedReport(reportPath);
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
}

/**
 * 生成详细报告
 * @param {string} reportPath 报告文件路径
 */
function generateDetailedReport(reportPath) {
  let report = '# 视觉脚本系统节点统计报告\n\n';
  report += `生成时间: ${new Date().toLocaleString()}\n\n`;
  
  report += '## 总体统计\n\n';
  report += `- **总节点数量**: ${nodeStats.totalNodes}\n`;
  report += `- **节点文件数量**: ${Object.keys(nodeStats.nodesByFile).length}\n`;
  report += `- **节点类别数量**: ${Object.keys(nodeStats.nodesByCategory).length}\n\n`;
  
  report += '## 按类别统计\n\n';
  Object.entries(nodeStats.nodesByCategory)
    .sort((a, b) => b[1] - a[1])
    .forEach(([category, count]) => {
      report += `- **${category}**: ${count} 个节点\n`;
    });
  
  report += '\n## 按文件详细统计\n\n';
  Object.entries(nodeStats.nodesByFile)
    .sort((a, b) => b[1].nodeCount - a[1].nodeCount)
    .forEach(([fileName, data]) => {
      const category = getNodeCategory(fileName);
      report += `### ${fileName} (${category})\n\n`;
      report += `- **节点类数量**: ${data.nodeCount}\n`;
      report += `- **注册类型数量**: ${data.registeredCount}\n`;
      
      if (data.nodeClasses.length > 0) {
        report += `- **节点类列表**:\n`;
        data.nodeClasses.forEach(node => {
          report += `  - ${node}\n`;
        });
      }
      
      if (data.registeredTypes.length > 0) {
        report += `- **注册类型列表**:\n`;
        data.registeredTypes.forEach(type => {
          report += `  - ${type}\n`;
        });
      }
      
      report += '\n';
    });
  
  report += '## 所有节点列表\n\n';
  nodeStats.allNodes
    .sort((a, b) => a.name.localeCompare(b.name))
    .forEach(node => {
      report += `- **${node.name}** (${node.category} - ${node.file})\n`;
    });
  
  // 确保目录存在
  const reportDir = path.dirname(reportPath);
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, report, 'utf8');
}

// 运行主函数
main();
