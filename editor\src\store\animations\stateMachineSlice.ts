/**
 * 动画状态机状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { AnimationStateData, AnimationStateMachineData, ParameterData, TransitionRuleData } from '../../libs/dl-engine-types';
import { stateMachineService } from '../../services/stateMachineService';

// 节点位置接口
export interface NodePosition {
  x: number;
  y: number;
}

// 状态机状态
interface StateMachineState {
  // 状态机数据
  stateMachine: AnimationStateMachineData | null;
  // 选中的状态名称
  selectedState: string | null;
  // 选中的转换规则
  selectedTransition: { from: string; to: string } | null;
  // 是否处于编辑模式
  isEditing: boolean;
  // 是否处于调试模式
  isDebugging: boolean;
  // 是否显示网格
  showGrid: boolean;
  // 是否显示参数面板
  showParameters: boolean;
  // 是否显示调试面板
  showDebugger: boolean;
  // 缩放级别
  zoom: number;
  // 画布偏移
  offset: { x: number; y: number };
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: StateMachineState = {
  stateMachine: null,
  selectedState: null,
  selectedTransition: null,
  isEditing: false,
  isDebugging: false,
  showGrid: true,
  showParameters: true,
  showDebugger: false,
  zoom: 1,
  offset: { x: 0, y: 0 },
  loading: false,
  error: null
};

// 加载状态机
export const loadStateMachine = createAsyncThunk(
  'stateMachine/load',
  async (entityId: string, { rejectWithValue }) => {
    try {
      const data = await stateMachineService.loadStateMachine(entityId);
      return data;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// 保存状态机
export const saveStateMachine = createAsyncThunk(
  'stateMachine/save',
  async ({ entityId, data }: { entityId: string; data: AnimationStateMachineData }, { rejectWithValue }) => {
    try {
      await stateMachineService.saveStateMachine(entityId, data);
      return data;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// 状态机切片
const stateMachineSlice = createSlice({
  name: 'stateMachine',
  initialState,
  reducers: {
    // 设置状态机数据
    setStateMachine: (state, action: PayloadAction<AnimationStateMachineData>) => {
      state.stateMachine = action.payload;
    },
    
    // 添加状态
    addState: (state, action: PayloadAction<AnimationStateData>) => {
      if (!state.stateMachine) return;
      
      // 检查是否已存在同名状态
      const exists = state.stateMachine.states.some(s => s.name === action.payload.name);
      if (exists) return;
      
      state.stateMachine.states.push(action.payload);
      state.selectedState = action.payload.name;
    },
    
    // 更新状态
    updateState: (state, action: PayloadAction<{ name: string; state: Partial<AnimationStateData> }>) => {
      if (!state.stateMachine) return;
      
      const index = state.stateMachine.states.findIndex(s => s.name === action.payload.name);
      if (index === -1) return;
      
      state.stateMachine.states[index] = {
        ...state.stateMachine.states[index],
        ...action.payload.state
      };
    },
    
    // 移除状态
    removeState: (state, action: PayloadAction<string>) => {
      if (!state.stateMachine) return;
      
      // 移除相关的转换规则
      state.stateMachine.transitions = state.stateMachine.transitions.filter(
        t => t.from !== action.payload && t.to !== action.payload
      );
      
      // 移除状态
      state.stateMachine.states = state.stateMachine.states.filter(s => s.name !== action.payload);
      
      // 如果移除的是当前选中的状态，则清除选中状态
      if (state.selectedState === action.payload) {
        state.selectedState = null;
      }
      
      // 如果移除的是当前状态，则清除当前状态
      if (state.stateMachine.currentState === action.payload) {
        state.stateMachine.currentState = undefined;
      }
    },
    
    // 添加转换规则
    addTransition: (state, action: PayloadAction<TransitionRuleData>) => {
      if (!state.stateMachine) return;
      
      // 检查是否已存在相同的转换规则
      const exists = state.stateMachine.transitions.some(
        t => t.from === action.payload.from && t.to === action.payload.to
      );
      if (exists) return;
      
      state.stateMachine.transitions.push(action.payload);
      state.selectedTransition = { from: action.payload.from, to: action.payload.to };
    },
    
    // 更新转换规则
    updateTransition: (state, action: PayloadAction<{ from: string; to: string; transition: Partial<TransitionRuleData> }>) => {
      if (!state.stateMachine) return;
      
      const index = state.stateMachine.transitions.findIndex(
        t => t.from === action.payload.from && t.to === action.payload.to
      );
      if (index === -1) return;
      
      state.stateMachine.transitions[index] = {
        ...state.stateMachine.transitions[index],
        ...action.payload.transition
      };
    },
    
    // 移除转换规则
    removeTransition: (state, action: PayloadAction<{ from: string; to: string }>) => {
      if (!state.stateMachine) return;
      
      state.stateMachine.transitions = state.stateMachine.transitions.filter(
        t => !(t.from === action.payload.from && t.to === action.payload.to)
      );
      
      // 如果移除的是当前选中的转换规则，则清除选中转换规则
      if (
        state.selectedTransition &&
        state.selectedTransition.from === action.payload.from &&
        state.selectedTransition.to === action.payload.to
      ) {
        state.selectedTransition = null;
      }
    },
    
    // 添加参数
    addParameter: (state, action: PayloadAction<ParameterData>) => {
      if (!state.stateMachine) return;
      
      // 检查是否已存在同名参数
      const exists = state.stateMachine.parameters.some(p => p.name === action.payload.name);
      if (exists) return;
      
      state.stateMachine.parameters.push(action.payload);
    },
    
    // 更新参数
    updateParameter: (state, action: PayloadAction<{ name: string; parameter: Partial<ParameterData> }>) => {
      if (!state.stateMachine) return;
      
      const index = state.stateMachine.parameters.findIndex(p => p.name === action.payload.name);
      if (index === -1) return;
      
      state.stateMachine.parameters[index] = {
        ...state.stateMachine.parameters[index],
        ...action.payload.parameter
      };
    },
    
    // 移除参数
    removeParameter: (state, action: PayloadAction<string>) => {
      if (!state.stateMachine) return;
      
      state.stateMachine.parameters = state.stateMachine.parameters.filter(p => p.name !== action.payload);
    },
    
    // 设置选中状态
    setSelectedState: (state, action: PayloadAction<string | null>) => {
      state.selectedState = action.payload;
      
      // 如果选中了状态，则清除选中的转换规则
      if (action.payload !== null) {
        state.selectedTransition = null;
      }
    },
    
    // 设置选中转换规则
    setSelectedTransition: (state, action: PayloadAction<{ from: string; to: string } | null>) => {
      state.selectedTransition = action.payload;
      
      // 如果选中了转换规则，则清除选中的状态
      if (action.payload !== null) {
        state.selectedState = null;
      }
    },
    
    // 设置编辑模式
    setEditingMode: (state, action: PayloadAction<boolean>) => {
      state.isEditing = action.payload;
    },
    
    // 设置调试模式
    setDebuggingMode: (state, action: PayloadAction<boolean>) => {
      state.isDebugging = action.payload;
    },
    
    // 设置显示网格
    setShowGrid: (state, action: PayloadAction<boolean>) => {
      state.showGrid = action.payload;
    },
    
    // 设置显示参数面板
    setShowParameters: (state, action: PayloadAction<boolean>) => {
      state.showParameters = action.payload;
    },
    
    // 设置显示调试面板
    setShowDebugger: (state, action: PayloadAction<boolean>) => {
      state.showDebugger = action.payload;
    },
    
    // 设置缩放级别
    setZoom: (state, action: PayloadAction<number>) => {
      state.zoom = action.payload;
    },
    
    // 设置画布偏移
    setOffset: (state, action: PayloadAction<{ x: number; y: number }>) => {
      state.offset = action.payload;
    },
    
    // 更新状态位置
    updateStatePosition: (state, action: PayloadAction<{ name: string; position: NodePosition }>) => {
      if (!state.stateMachine) return;
      
      const index = state.stateMachine.states.findIndex(s => s.name === action.payload.name);
      if (index === -1) return;
      
      state.stateMachine.states[index].position = action.payload.position;
    },
    
    // 清空状态机
    clearStateMachine: (state) => {
      state.stateMachine = null;
      state.selectedState = null;
      state.selectedTransition = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // 加载状态机
      .addCase(loadStateMachine.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadStateMachine.fulfilled, (state, action) => {
        state.loading = false;
        state.stateMachine = action.payload;
      })
      .addCase(loadStateMachine.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 保存状态机
      .addCase(saveStateMachine.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveStateMachine.fulfilled, (state) => {
        state.loading = false;
        state.isEditing = false;
      })
      .addCase(saveStateMachine.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  setStateMachine,
  addState,
  updateState,
  removeState,
  addTransition,
  updateTransition,
  removeTransition,
  addParameter,
  updateParameter,
  removeParameter,
  setSelectedState,
  setSelectedTransition,
  setEditingMode,
  setDebuggingMode,
  setShowGrid,
  setShowParameters,
  setShowDebugger,
  setZoom,
  setOffset,
  updateStatePosition,
  clearStateMachine
} = stateMachineSlice.actions;

export default stateMachineSlice.reducer;
